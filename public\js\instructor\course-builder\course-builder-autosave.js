/**
 * Course Builder Auto-save Module
 * Handles auto-save functionality for forms
 */

function initializeAutoSave() {
    // Enable auto-save functionality
    initializeCourseAutoSave();
}

function initializeCourseAutoSave() {
    // Auto-save for course details
    const courseForm = document.getElementById('course-details-form');
    if (courseForm) {
        const inputs = courseForm.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            // Remove existing listeners to prevent duplicates
            input.removeEventListener('input', input._autoSaveHandler);
            input.removeEventListener('change', input._autoSaveHandler);

            const eventType = input.tagName === 'SELECT' ? 'change' : 'input';
            const handler = function() {
                scheduleAutoSave('course', courseId, () => autoSaveCourse());
            };

            // Store handler reference for removal
            input._autoSaveHandler = handler;
            input.addEventListener(eventType, handler);
        });
    }
}

function scheduleAutoSave(type, id, saveFunction) {
    const key = `${type}-${id}`;

    console.log('Scheduling auto-save for:', key);

    // Clear existing timeout
    if (autoSaveTimeouts.has(key)) {
        clearTimeout(autoSaveTimeouts.get(key));
        console.log('Cleared existing timeout for:', key);
    }

    // Show saving indicator using the utility function
    if (typeof showSaveStatus === 'function') {
        showSaveStatus('saving', 'Saving...');
    } else {
        console.warn('showSaveStatus function not available');
    }

    // Schedule new save
    const timeoutId = setTimeout(() => {
        console.log('Executing auto-save for:', key);
        try {
            saveFunction();
        } catch (error) {
            console.error('Error in save function:', error);
            if (typeof showError === 'function') {
                showError('Save function error: ' + error.message);
            } else {
                console.error('showError function not available');
            }
        }
        autoSaveTimeouts.delete(key);
    }, 1000); // 1 second delay

    autoSaveTimeouts.set(key, timeoutId);
}

// Remove the duplicate showSaveStatus function - use the one from course-builder-utils.js
// This function was causing infinite recursion by checking for window.showSaveStatus
// which pointed back to itself

function showFileUploadIndicator(lectureId, show) {
    const indicator = document.getElementById(`file-upload-indicator-${lectureId}`);
    if (!indicator) return;

    if (show) {
        indicator.classList.remove('hidden');
    } else {
        indicator.classList.add('hidden');
    }
}

/**
 * Show immediate visual feedback when Published/Free Preview checkboxes are changed for lectures
 */
function showCheckboxChangeIndicator(lectureId, checkbox) {
    // Create or get the immediate feedback element
    let feedbackElement = document.getElementById(`immediate-feedback-${lectureId}-${checkbox.name}`);
    
    if (!feedbackElement) {
        feedbackElement = document.createElement('div');
        feedbackElement.id = `immediate-feedback-${lectureId}-${checkbox.name}`;
        feedbackElement.className = 'inline-flex items-center ml-2 px-2 py-1 rounded-full text-xs font-medium transition-all duration-300';
        
        // Insert next to the checkbox
        const checkboxContainer = checkbox.closest('label') || checkbox.parentElement;
        if (checkboxContainer) {
            checkboxContainer.appendChild(feedbackElement);
        }
    }
    
    // Update the feedback based on checkbox state
    const isChecked = checkbox.checked;
    const isPublished = checkbox.name === 'is_published';
    
    if (isPublished) {
        if (isChecked) {
            feedbackElement.className = 'inline-flex items-center ml-2 px-2 py-1 rounded-full text-xs font-medium transition-all duration-300 bg-green-600/20 text-green-400 border border-green-600/30';
            feedbackElement.innerHTML = '<i class="fas fa-eye mr-1"></i>Published';
        } else {
            feedbackElement.className = 'inline-flex items-center ml-2 px-2 py-1 rounded-full text-xs font-medium transition-all duration-300 bg-gray-600/20 text-gray-400 border border-gray-600/30';
            feedbackElement.innerHTML = '<i class="fas fa-eye-slash mr-1"></i>Draft';
        }
    } else { // is_free_preview
        if (isChecked) {
            feedbackElement.className = 'inline-flex items-center ml-2 px-2 py-1 rounded-full text-xs font-medium transition-all duration-300 bg-blue-600/20 text-blue-400 border border-blue-600/30';
            feedbackElement.innerHTML = '<i class="fas fa-unlock mr-1"></i>Free Preview';
        } else {
            feedbackElement.className = 'inline-flex items-center ml-2 px-2 py-1 rounded-full text-xs font-medium transition-all duration-300 bg-gray-600/20 text-gray-400 border border-gray-600/30';
            feedbackElement.innerHTML = '<i class="fas fa-lock mr-1"></i>Paid Content';
        }
    }
    
    // Add a subtle pulse animation to indicate the change
    feedbackElement.style.transform = 'scale(1.1)';
    setTimeout(() => {
        feedbackElement.style.transform = 'scale(1)';
    }, 200);
    
    // Show saving indicator
    if (typeof showSaveStatus === 'function') {
        showSaveStatus('saving', `Saving ${isPublished ? 'publish status' : 'preview status'}...`);
    }
}

/**
 * Show immediate visual feedback when Published/Free Preview checkboxes are changed for chapters
 */
function showChapterCheckboxChangeIndicator(chapterId, checkbox) {
    // Create or get the immediate feedback element
    let feedbackElement = document.getElementById(`immediate-feedback-${chapterId}-${checkbox.name}`);
    
    if (!feedbackElement) {
        feedbackElement = document.createElement('div');
        feedbackElement.id = `immediate-feedback-${chapterId}-${checkbox.name}`;
        feedbackElement.className = 'inline-flex items-center ml-2 px-2 py-1 rounded-full text-xs font-medium transition-all duration-300';
        
        // Insert next to the checkbox
        const checkboxContainer = checkbox.closest('label') || checkbox.parentElement;
        if (checkboxContainer) {
            checkboxContainer.appendChild(feedbackElement);
        }
    }
    
    // Update the feedback based on checkbox state
    const isChecked = checkbox.checked;
    const isPublished = checkbox.name === 'is_published';
    
    if (isPublished) {
        if (isChecked) {
            feedbackElement.className = 'inline-flex items-center ml-2 px-2 py-1 rounded-full text-xs font-medium transition-all duration-300 bg-green-600/20 text-green-400 border border-green-600/30';
            feedbackElement.innerHTML = '<i class="fas fa-eye mr-1"></i>Published';
        } else {
            feedbackElement.className = 'inline-flex items-center ml-2 px-2 py-1 rounded-full text-xs font-medium transition-all duration-300 bg-gray-600/20 text-gray-400 border border-gray-600/30';
            feedbackElement.innerHTML = '<i class="fas fa-eye-slash mr-1"></i>Draft';
        }
    } else { // is_free_preview
        if (isChecked) {
            feedbackElement.className = 'inline-flex items-center ml-2 px-2 py-1 rounded-full text-xs font-medium transition-all duration-300 bg-blue-600/20 text-blue-400 border border-blue-600/30';
            feedbackElement.innerHTML = '<i class="fas fa-unlock mr-1"></i>Free Preview';
        } else {
            feedbackElement.className = 'inline-flex items-center ml-2 px-2 py-1 rounded-full text-xs font-medium transition-all duration-300 bg-gray-600/20 text-gray-400 border border-gray-600/30';
            feedbackElement.innerHTML = '<i class="fas fa-lock mr-1"></i>Paid Content';
        }
    }
    
    // Add a subtle pulse animation to indicate the change
    feedbackElement.style.transform = 'scale(1.1)';
    setTimeout(() => {
        feedbackElement.style.transform = 'scale(1)';
    }, 200);
    
    // Removed persistent saving indicator to prevent infinite loading
}

function initializeChapterAutoSave(chapterId) {
    const chapterForm = document.getElementById(`chapter-form-${chapterId}`);
    if (chapterForm) {
        const inputs = chapterForm.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            // Remove existing listeners to prevent duplicates
            input.removeEventListener('input', input._autoSaveHandler);
            input.removeEventListener('change', input._autoSaveHandler);

            // Special immediate handling for Published checkbox
            if (input.type === 'checkbox' && input.name === 'is_published') {
                const immediateHandler = function() {
                    // Show immediate visual feedback
                    showChapterCheckboxChangeIndicator(chapterId, input);
                    
                    // Save immediately for critical status changes
                    setTimeout(() => {
                        autoSaveChapter(chapterId);
                    }, 50); // Very short delay for immediate feedback
                };
                
                input._autoSaveHandler = immediateHandler;
                input.addEventListener('change', immediateHandler);
            } else {
                // Standard auto-save for other inputs
                const eventType = input.type === 'checkbox' || input.tagName === 'SELECT' ? 'change' : 'input';
                const handler = function() {
                    scheduleAutoSave('chapter', chapterId, () => autoSaveChapter(chapterId));
                };

                // Store handler reference for removal
                input._autoSaveHandler = handler;
                input.addEventListener(eventType, handler);
            }
        });
    }
}

function initializeLectureAutoSave(lectureId) {
    const lectureForm = document.getElementById(`lecture-form-${lectureId}`);
    if (!lectureForm) {
        console.error('Lecture form not found for auto-save initialization:', lectureId);
        return;
    }

    // Get all form inputs, including those in hidden content sections
    const inputs = lectureForm.querySelectorAll('input, textarea, select');

    inputs.forEach(input => {
        // Remove existing listeners to prevent duplicates
        if (input._lectureAutoSaveHandler) {
            input.removeEventListener('input', input._lectureAutoSaveHandler);
            input.removeEventListener('change', input._lectureAutoSaveHandler);
        }

        // Special handling for file inputs
        if (input.type === 'file' && input.name === 'resource_file') {
            const fileHandler = function() {
                if (input.files && input.files.length > 0) {
                    console.log('File selected for lecture:', lectureId, input.files[0].name);
                    // Auto-save when file is selected
                    setTimeout(() => {
                        autoSaveLecture(lectureId);
                    }, 500);
                }
            };
            input._lectureAutoSaveHandler = fileHandler;
            input.addEventListener('change', fileHandler);
        } else {
            // Regular form inputs
            const eventType = input.type === 'checkbox' || input.tagName === 'SELECT' ? 'change' : 'input';
            
            // Special immediate handling for Published and Free Preview checkboxes
            if (input.type === 'checkbox' && (input.name === 'is_published' || input.name === 'is_free_preview')) {
                const immediateHandler = function() {
                    // Show immediate visual feedback
                    showCheckboxChangeIndicator(lectureId, input);
                    
                    // Save immediately for critical status changes
                    setTimeout(() => {
                        autoSaveLecture(lectureId);
                    }, 50); // Very short delay for immediate feedback
                };
                
                input._lectureAutoSaveHandler = immediateHandler;
                input.addEventListener('change', immediateHandler);
            } else {
                // Standard auto-save for other inputs
                const handler = function() {
                    // Add a small delay to ensure the form state is updated
                    setTimeout(() => {
                        scheduleAutoSave('lecture', lectureId, () => autoSaveLecture(lectureId));
                    }, 100);
                };

                input._lectureAutoSaveHandler = handler;
                input.addEventListener(eventType, handler);
            }
        }
    });
}
